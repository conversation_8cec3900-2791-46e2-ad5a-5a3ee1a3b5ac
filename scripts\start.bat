@echo off
chcp 65001 >nul
echo Starting XUST High-level Talent Management System...

echo.
echo Checking environment...

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Docker not installed or not started
    echo.
    echo Please install Docker Desktop first:
    echo Download: https://www.docker.com/products/docker-desktop/
    echo After installation, please restart computer and start Docker Desktop
    echo.
    pause
    exit /b 1
) else (
    echo ✓ Docker installed
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Docker Compose not installed
    echo.
    echo Please ensure Docker Compose is properly installed
    echo Usually Docker Desktop includes Docker Compose automatically
    echo.
    pause
    exit /b 1
) else (
    echo ✓ Docker Compose installed
)

REM Check if Docker service is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo Docker service not running
    echo.
    echo Please start Docker Desktop service
    echo 1. Open Docker Desktop application
    echo 2. Wait for Docker engine to start completely
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
) else (
    echo ✓ Docker service is running
)

REM Start services
echo.
echo Environment check passed, starting services...
echo Building and starting all services...

docker-compose up --build -d
if %errorlevel% equ 0 (
    echo.
    echo ✓ Services started successfully!
    echo Frontend access: http://localhost
    echo Backend API: http://localhost:3000
    echo.
    echo Common commands:
    echo Check service status: docker-compose ps
    echo View logs: docker-compose logs -f
    echo Stop services: stop.bat or docker-compose down
) else (
    echo.
    echo Service startup failed, please check error messages
)

echo.
pause