#!/bin/bash

# GCC-XKD One-Click Deployment Script (Linux/macOS)
echo "GCC-XKD One-Click Deployment Tool"
echo "=================================="

# Check Docker environment
check_docker() {
    echo "Checking Docker environment..."

    if ! command -v docker &> /dev/null; then
        echo "Docker not installed"
        return 1
    fi
    echo "✓ Docker installed"

    if ! command -v docker-compose &> /dev/null; then
        echo "Docker Compose not installed"
        return 1
    fi
    echo "✓ Docker Compose installed"

    if ! docker info &> /dev/null; then
        echo "Docker service not running"
        return 1
    fi
    echo "✓ Docker service is running"

    return 0
}

# Check deployment package integrity
check_deployment_package() {
    echo "Checking deployment package integrity..."

    local missing=()

    if [ ! -d "images" ]; then
        missing+=("Image files directory (images/)")
    else
        echo "✓ Image files directory"
    fi

    if [ ! -d "config" ]; then
        missing+=("Configuration files directory (config/)")
    else
        echo "✓ Configuration files directory"
    fi

    if [ ! -f "config/docker-compose.yml" ]; then
        missing+=("Docker Compose configuration")
    else
        echo "✓ Docker Compose configuration"
    fi

    if [ ${#missing[@]} -gt 0 ]; then
        echo "Missing the following files/directories:"
        for item in "${missing[@]}"; do
            echo "  - $item"
        done
        return 1
    fi

    echo "✓ Deployment package complete"
    return 0
}

# Import images
import_images() {
    echo "Importing Docker images..."

    if [ ! -d "images" ]; then
        echo "Images directory not found"
        return 1
    fi

    local tar_files=($(find images -name "*.tar" 2>/dev/null))

    if [ ${#tar_files[@]} -eq 0 ]; then
        echo "No image files found"
        return 1
    fi

    for tar_file in "${tar_files[@]}"; do
        echo "Importing: $(basename "$tar_file")"
        if docker load -i "$tar_file"; then
            echo "✓ Import completed: $(basename "$tar_file")"
        else
            echo "Import failed: $(basename "$tar_file")"
            return 1
        fi
    done

    echo "✓ Image import completed"
    return 0
}

# Start services
start_services() {
    echo "Starting services..."

    # Copy configuration file to current directory
    if [ -f "config/docker-compose.yml" ]; then
        cp config/docker-compose.yml .
        echo "✓ Configuration file copied to current directory"
    else
        echo "docker-compose.yml file not found"
        return 1
    fi

    # Start services
    if docker-compose up -d; then
        echo "✓ Services started successfully"
        return 0
    else
        echo "Failed to start services"
        return 1
    fi
}

# Show access information
show_access_info() {
    echo ""
    echo "Deployment completed!"
    echo "===================="
    echo ""
    echo "Access URLs:"
    echo "- Frontend: http://localhost"
    echo "- Backend API: http://localhost:3000"
    echo "- MongoDB: localhost:27018"
    echo ""
    echo "Common commands:"
    echo "- Check status: docker-compose ps"
    echo "- View logs: docker-compose logs -f"
    echo "- Stop services: docker-compose down"
    echo "- Restart services: docker-compose restart"
}

# Main process
echo ""

# 1. Check environment
if ! check_docker; then
    echo ""
    echo "Please install and start Docker environment first"
    exit 1
fi

echo ""

# 2. Check deployment package integrity
if ! check_deployment_package; then
    exit 1
fi

echo ""

# 3. Import images
if ! import_images; then
    exit 1
fi

echo ""

# 4. Start services
if ! start_services; then
    exit 1
fi

# 5. Show access information
show_access_info

echo ""
read -p "Press any key to exit..."
