# Configure Docker China Mirror Sources

param()

Write-Host "Configure Docker China Mirror Sources" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green

# Docker Desktop configuration file path
$dockerConfigPath = "$env:USERPROFILE\.docker"
$daemonConfigFile = "$dockerConfigPath\daemon.json"

# Ensure configuration directory exists
if (-not (Test-Path $dockerConfigPath)) {
    New-Item -ItemType Directory -Path $dockerConfigPath -Force | Out-Null
    Write-Host "✓ Created Docker configuration directory" -ForegroundColor Green
}

# Mirror source configuration
$daemonConfig = @{
    "registry-mirrors" = @(
        "https://docker.mirrors.ustc.edu.cn",
        "https://hub-mirror.c.163.com",
        "https://mirror.baidubce.com",
        "https://dockerproxy.com"
    )
    "dns" = @("*******", "***************", "*********")
    "insecure-registries" = @()
    "experimental" = $false
} | ConvertTo-Json -Depth 3

# Backup existing configuration
if (Test-Path $daemonConfigFile) {
    $backupFile = "$daemonConfigFile.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    Copy-Item $daemonConfigFile $backupFile
    Write-Host "✓ Backed up existing configuration to: $backupFile" -ForegroundColor Yellow
}

# Write new configuration
try {
    $daemonConfig | Out-File -FilePath $daemonConfigFile -Encoding UTF8
    Write-Host "✓ Docker mirror source configuration completed" -ForegroundColor Green
    Write-Host ""
    Write-Host "Configuration file location: $daemonConfigFile" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Configuration content:" -ForegroundColor Yellow
    Write-Host $daemonConfig -ForegroundColor White
    Write-Host ""
    Write-Host "Please restart Docker Desktop for the configuration to take effect" -ForegroundColor Red
    Write-Host "After restart, run: .\fix-docker-network.ps1" -ForegroundColor Yellow
} catch {
    Write-Host "Configuration write failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press any key to exit"

