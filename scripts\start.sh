#!/bin/bash

echo "Starting GCC-XKD High-level Talent Management System..."

# Color definitions
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Check if Docker is installed
check_docker() {
    if command -v docker &> /dev/null; then
        echo -e "${GREEN}✓ Docker installed: $(docker --version)${NC}"
        return 0
    else
        echo -e "${RED}Docker not installed${NC}"
        return 1
    fi
}

# Check if Docker Compose is installed
check_docker_compose() {
    if command -v docker-compose &> /dev/null; then
        echo -e "${GREEN}✓ Docker Compose installed: $(docker-compose --version)${NC}"
        return 0
    else
        echo -e "${RED}Docker Compose not installed${NC}"
        return 1
    fi
}

# Check if Docker service is running
check_docker_running() {
    if docker info &> /dev/null; then
        echo -e "${GREEN}✓ Docker service is running${NC}"
        return 0
    else
        echo -e "${RED}Docker service not running${NC}"
        return 1
    fi
}

# Environment check
echo -e "\n${YELLOW}Checking environment...${NC}"

if ! check_docker; then
    echo -e "\n${RED}Please install Docker first:${NC}"
    echo -e "${CYAN}Ubuntu/Debian: sudo apt-get install docker.io${NC}"
    echo -e "${CYAN}CentOS/RHEL: sudo yum install docker${NC}"
    echo -e "${CYAN}macOS: brew install docker or download Docker Desktop${NC}"
    exit 1
fi

if ! check_docker_compose; then
    echo -e "\n${RED}Please install Docker Compose first:${NC}"
    echo -e "${CYAN}sudo curl -L \"https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-\$(uname -s)-\$(uname -m)\" -o /usr/local/bin/docker-compose${NC}"
    echo -e "${CYAN}sudo chmod +x /usr/local/bin/docker-compose${NC}"
    exit 1
fi

if ! check_docker_running; then
    echo -e "\n${RED}Please start Docker service:${NC}"
    echo -e "${CYAN}sudo systemctl start docker${NC}"
    echo -e "${CYAN}or start Docker Desktop application${NC}"
    exit 1
fi

# Start services
echo -e "\n${GREEN}Environment check passed, starting services...${NC}"

if docker-compose up --build -d; then
    echo ""
    echo -e "${GREEN}✓ Services started successfully!${NC}"
    echo -e "${CYAN}Frontend URL: http://localhost${NC}"
    echo -e "${CYAN}Backend API URL: http://localhost:3000${NC}"
    echo ""
    echo -e "${YELLOW}Common commands:${NC}"
    echo "Check service status: docker-compose ps"
    echo "View logs: docker-compose logs -f"
    echo "Stop services: ./stop.sh or docker-compose down"
else
    echo -e "\n${RED}Failed to start services, please check error messages${NC}"
    exit 1
fi


