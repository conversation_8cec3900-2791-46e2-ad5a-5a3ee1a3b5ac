# GCC-XKD One-Click Deployment Script - Local Development Environment

param(
    [string]$ImagePackage = ""
)

Write-Host "GCC-XKD One-Click Deployment Tool" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green

# Check Docker environment
function Test-DockerEnvironment {
    Write-Host "Checking Docker environment..." -ForegroundColor Yellow

    try {
        & docker --version | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Docker not installed" -ForegroundColor Red
            return $false
        }
        Write-Host "Docker installed" -ForegroundColor Green
    } catch {
        Write-Host "Docker not installed" -ForegroundColor Red
        return $false
    }

    try {
        & docker info 2>$null | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Docker service not started" -ForegroundColor Red
            return $false
        }
        Write-Host "Docker service is running" -ForegroundColor Green
    } catch {
        Write-Host "Docker service not started" -ForegroundColor Red
        return $false
    }

    try {
        & docker-compose --version | Out-Null
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Docker Compose not installed" -ForegroundColor Red
            return $false
        }
        Write-Host "Docker Compose installed" -ForegroundColor Green
    } catch {
        Write-Host "Docker Compose not installed" -ForegroundColor Red
        return $false
    }

    return $true
}

# Check port availability
function Test-PortAvailability {
    Write-Host "Checking port availability..." -ForegroundColor Yellow

    $ports = @(80, 3000, 27018)
    $allAvailable = $true

    foreach ($port in $ports) {
        try {
            $connection = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
            if ($connection.TcpTestSucceeded) {
                Write-Host ("Port " + $port + " is in use") -ForegroundColor Red
                $allAvailable = $false
            } else {
                Write-Host ("Port " + $port + " is available") -ForegroundColor Green
            }
        } catch {
            Write-Host ("Port " + $port + " is available") -ForegroundColor Green
        }
    }

    return $allAvailable
}

# Import image package
function Import-ImagePackage {
    param([string]$PackagePath)

    if (-not $PackagePath) {
        Write-Host "No image package path specified, skipping import" -ForegroundColor Yellow
        return $true
    }

    if (-not (Test-Path $PackagePath)) {
        Write-Host ("Image package does not exist: " + $PackagePath) -ForegroundColor Red
        return $false
    }

    Write-Host ("Importing image package: " + $PackagePath) -ForegroundColor Yellow

    if ($PackagePath.EndsWith(".tar")) {
        # Single image file
        & docker load -i $PackagePath
        return $LASTEXITCODE -eq 0
    } elseif (Test-Path (Join-Path $PackagePath "images")) {
        # Image directory
        $imageFiles = Get-ChildItem (Join-Path $PackagePath "images") -Filter "*.tar"
        foreach ($imageFile in $imageFiles) {
            Write-Host ("Importing: " + $imageFile.Name) -ForegroundColor Cyan
            & docker load -i $imageFile.FullName
            if ($LASTEXITCODE -ne 0) {
                Write-Host ("Import failed: " + $imageFile.Name) -ForegroundColor Red
                return $false
            }
        }
        Write-Host "Image package import completed" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Invalid image package format" -ForegroundColor Red
        return $false
    }
}

# Build images
function Build-Images {
    Write-Host "Building Docker images..." -ForegroundColor Yellow

    & docker-compose build
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Image build completed" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Image build failed" -ForegroundColor Red
        return $false
    }
}

# Start services
function Start-Services {
    Write-Host "Starting services..." -ForegroundColor Yellow

    & docker-compose up -d
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Services started successfully" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Failed to start services" -ForegroundColor Red
        return $false
    }
}

# Wait for services to be ready
function Wait-ServicesReady {
    Write-Host "Waiting for services to be ready..." -ForegroundColor Yellow

    $maxWait = 60
    $waited = 0

    while ($waited -lt $maxWait) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:3000/health" -TimeoutSec 5 -ErrorAction SilentlyContinue
            if ($response.StatusCode -eq 200) {
                Write-Host "Backend service is ready" -ForegroundColor Green
                break
            }
        } catch {
            # Continue waiting
        }

        Start-Sleep -Seconds 2
        $waited += 2
        Write-Host ("Waiting... (" + $waited + "/" + $maxWait + "s)") -ForegroundColor Cyan
    }

    if ($waited -ge $maxWait) {
        Write-Host "! Service startup timeout, but may still be initializing" -ForegroundColor Yellow
    }
}

# Show access information
function Show-AccessInfo {
    Write-Host ""
    Write-Host "Deployment completed!" -ForegroundColor Green
    Write-Host "====================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Access URLs:" -ForegroundColor Cyan
    Write-Host "- Frontend: http://localhost" -ForegroundColor White
    Write-Host "- Backend API: http://localhost:3000" -ForegroundColor White
    Write-Host "- MongoDB: localhost:27018" -ForegroundColor White
    Write-Host ""
    Write-Host "Common commands:" -ForegroundColor Cyan
    Write-Host "- Check status: docker-compose ps" -ForegroundColor White
    Write-Host "- View logs: docker-compose logs -f" -ForegroundColor White
    Write-Host "- Stop services: docker-compose down" -ForegroundColor White
    Write-Host "- Restart services: docker-compose restart" -ForegroundColor White
    Write-Host ""
    Write-Host "MongoDB connection tool: .\scripts\connect-mongodb.ps1" -ForegroundColor Yellow
}

# Main program
Write-Host ""

# 1. Check environment
if (-not (Test-DockerEnvironment)) {
    Write-Host ""
    Write-Host "Please install and start Docker environment first" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# 2. Check ports
if (-not (Test-PortAvailability)) {
    Write-Host ""
    Write-Host "Please free up occupied ports and try again" -ForegroundColor Red
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# 3. Import image package (if specified)
if ($ImagePackage) {
    if (-not (Import-ImagePackage $ImagePackage)) {
        Read-Host "Press any key to exit"
        exit 1
    }
    Write-Host ""
}

# 4. Build images
if (-not (Build-Images)) {
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# 5. Start services
if (-not (Start-Services)) {
    Read-Host "Press any key to exit"
    exit 1
}

Write-Host ""

# 6. Wait for services to be ready
Wait-ServicesReady

# 7. Show access information
Show-AccessInfo

Write-Host ""
Read-Host "Press any key to exit"
